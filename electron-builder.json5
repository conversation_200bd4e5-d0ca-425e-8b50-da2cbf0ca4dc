// @see - https://www.electron.build/configuration/configuration
{
  "$schema": "https://raw.githubusercontent.com/electron-userland/electron-builder/master/packages/app-builder-lib/scheme.json",
  "appId": "com.icoconverter.app",
  "asar": true,
  "productName": "ICO格式转换器",
  "copyright": "Copyright © 2024 ICO Converter Team",
  "directories": {
    "output": "release/${version}"
  },
  "extraResources": [
    {
      "from": "public/icons/",
      "to": "icons/",
      "filter": ["**/*"]
    }
  ],
  "files": [
    "dist",
    "dist-electron"
  ],
  "mac": {
    "icon": "public/icons/icon-512x512.png",
    "target": [
      "dmg"
    ],
    "artifactName": "${productName}-Mac-${version}-Installer.${ext}",
    "category": "public.app-category.graphics-design"
  },
  "win": {
    "icon": "public/icons/icon-256x256.png",
    "target": [
      {
        "target": "nsis",
        "arch": [
          "x64"
        ]
      }
    ],
    "artifactName": "${productName}-Windows-${version}-Setup.${ext}",
    "requestedExecutionLevel": "asInvoker"
  },
  "nsis": {
    "oneClick": false,
    "perMachine": false,
    "allowToChangeInstallationDirectory": true,
    "deleteAppDataOnUninstall": false
  },
  "linux": {
    "icon": "public/icons/icon-512x512.png",
    "target": [
      "AppImage"
    ],
    "artifactName": "${productName}-Linux-${version}.${ext}",
    "category": "Graphics"
  }
}
