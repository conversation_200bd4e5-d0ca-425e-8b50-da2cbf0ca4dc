# ICO格式转换器 - 使用说明

## 快速开始

### 启动应用
```bash
npm run dev
```

### 使用步骤

1. **添加图片文件**
   - 拖拽图片文件到应用窗口
   - 或点击拖拽区域选择文件
   - 支持格式：PNG、JPG、JPEG、SVG、GIF、BMP、WebP

2. **选择ICO尺寸**
   - 点击需要的尺寸按钮（16×16 到 256×256）
   - 可以选择多个尺寸
   - 默认已选择常用尺寸

3. **开始转换**
   - 点击"开始转换"按钮
   - 等待转换完成

4. **下载ICO文件**
   - 在转换结果中点击"下载"按钮
   - 文件会保存为 .ico 格式

## 应用特性

### 桌面应用风格
- ✅ 自定义标题栏
- ✅ 窗口控制按钮
- ✅ 应用图标
- ✅ 原生菜单栏
- ✅ 毛玻璃效果界面

### 转换功能
- ✅ 多格式支持
- ✅ 多尺寸输出
- ✅ 高质量缩放
- ✅ 批量处理
- ✅ 实时预览

### 用户体验
- ✅ 拖拽上传
- ✅ 进度显示
- ✅ 一键下载
- ✅ 错误处理

## 构建发布版本

```bash
npm run build
```

构建完成后，可执行文件位于 `release` 目录中。

## 技术实现

- **前端**: React + TypeScript + CSS3
- **桌面**: Electron + 自定义标题栏
- **图片处理**: Canvas API + 高质量缩放
- **ICO生成**: 自定义ICO文件格式实现
- **UI图标**: Lucide React

## 注意事项

1. 确保图片文件不要过大（建议小于10MB）
2. SVG文件会先转换为位图再生成ICO
3. 转换质量取决于原图质量
4. 支持透明背景的PNG图片
