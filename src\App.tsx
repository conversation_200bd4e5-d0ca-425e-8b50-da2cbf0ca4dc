import { useState, useRef } from 'react'
import {
  Upload,
  Image as ImageIcon,
  Download,
  X,
  FileImage,
  Sparkles
} from 'lucide-react'
import { ImageToICOConverter } from './utils/imageConverter'
import './App.css'

interface FileItem {
  id: string
  file: File
  name: string
  size: string
  type: string
  preview?: string
}

interface ConversionResult {
  id: string
  size: string
  downloadUrl: string
  preview: string
}

const ICO_SIZES = [16, 32, 48, 64, 128, 256]

function App() {
  const [currentFile, setCurrentFile] = useState<FileItem | null>(null)
  const [selectedSizes, setSelectedSizes] = useState<number[]>([16, 32, 48])
  const [isConverting, setIsConverting] = useState(false)
  const [conversionProgress, setConversionProgress] = useState(0)
  const [results, setResults] = useState<ConversionResult[]>([])
  const [isDragOver, setIsDragOver] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file || !file.type.startsWith('image/')) return

    const fileItem: FileItem = {
      id: Math.random().toString(36).substr(2, 9),
      file,
      name: file.name,
      size: formatFileSize(file.size),
      type: file.type
    }

    // 生成预览
    const reader = new FileReader()
    reader.onload = (e) => {
      fileItem.preview = e.target?.result as string
      setCurrentFile({ ...fileItem, preview: fileItem.preview })
    }
    reader.readAsDataURL(file)

    setCurrentFile(fileItem)
    setResults([]) // 清空之前的结果
  }

  // 处理拖拽
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    const file = e.dataTransfer.files[0]
    if (!file || !file.type.startsWith('image/')) return

    const fileItem: FileItem = {
      id: Math.random().toString(36).substr(2, 9),
      file,
      name: file.name,
      size: formatFileSize(file.size),
      type: file.type
    }

    // 生成预览
    const reader = new FileReader()
    reader.onload = (e) => {
      fileItem.preview = e.target?.result as string
      setCurrentFile({ ...fileItem, preview: fileItem.preview })
    }
    reader.readAsDataURL(file)

    setCurrentFile(fileItem)
    setResults([])
  }

  // 切换尺寸选择
  const toggleSize = (size: number) => {
    setSelectedSizes(prev => 
      prev.includes(size) 
        ? prev.filter(s => s !== size)
        : [...prev, size]
    )
  }

  // 开始转换
  const startConversion = async () => {
    if (!currentFile || selectedSizes.length === 0) return

    setIsConverting(true)
    setConversionProgress(0)
    setResults([])

    try {
      const converter = new ImageToICOConverter()
      const newResults: ConversionResult[] = []

      for (let i = 0; i < selectedSizes.length; i++) {
        const size = selectedSizes[i]
        setConversionProgress(Math.round(((i + 1) / selectedSizes.length) * 100))

        const icoBlob = await converter.convertToICO(currentFile.file, [size])
        const downloadUrl = URL.createObjectURL(icoBlob)
        
        // 创建预览
        const canvas = document.createElement('canvas')
        canvas.width = size
        canvas.height = size
        const ctx = canvas.getContext('2d')
        
        const img = new Image()
        img.onload = () => {
          ctx?.drawImage(img, 0, 0, size, size)
        }
        img.src = currentFile.preview || ''

        newResults.push({
          id: `${currentFile.id}-${size}`,
          size: `${size}×${size}`,
          downloadUrl,
          preview: canvas.toDataURL()
        })
      }

      setResults(newResults)
    } catch (error) {
      console.error('转换失败:', error)
    } finally {
      setIsConverting(false)
      setConversionProgress(0)
    }
  }

  // 清空所有
  const clearAll = () => {
    setCurrentFile(null)
    setResults([])
    setConversionProgress(0)
    setSelectedSizes([16, 32, 48])
  }

  return (
    <div className="app">
      {/* 标题栏 */}
      <div className="title-bar">
        <div className="title-bar-left">
          <Sparkles className="app-icon" size={24} />
          <h1 className="app-title">ICO格式转换器</h1>
        </div>
        <div className="window-controls">
          <button className="window-control minimize" />
          <button className="window-control maximize" />
          <button className="window-control close" />
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="main-content">
        {/* 步骤1: 上传图片 */}
        <div className="step-section">
          <h2 className="step-title">
            <span className="step-number">1</span>
            选择图片文件
          </h2>
          <div
            className={`drop-zone ${isDragOver ? 'drag-over' : ''} ${currentFile ? 'has-file' : ''}`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="drop-zone-icon" />
            <div className="drop-zone-text">
              {currentFile ? '点击更换图片' : '拖拽图片文件到这里'}
            </div>
            <div className="drop-zone-subtext">
              支持 PNG, JPG, JPEG, SVG, GIF, BMP, WebP 格式
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileSelect}
              style={{ display: 'none' }}
            />
          </div>

          {/* 当前选择的文件 */}
          {currentFile && (
            <div className="current-file">
              <div className="file-preview">
                {currentFile.preview ? (
                  <img src={currentFile.preview} alt="预览" className="file-preview-img" />
                ) : (
                  <FileImage className="file-icon" />
                )}
                <div className="file-details">
                  <h4>{currentFile.name}</h4>
                  <p>{currentFile.size} • {currentFile.type}</p>
                </div>
                <button
                  className="btn-remove"
                  onClick={clearAll}
                  title="移除文件"
                >
                  <X size={16} />
                </button>
              </div>
            </div>
          )}
        </div>

        {/* 步骤2: 选择ICO尺寸 */}
        {currentFile && (
          <div className="step-section">
            <h2 className="step-title">
              <span className="step-number">2</span>
              选择ICO尺寸
            </h2>
            <div className="size-grid">
              {ICO_SIZES.map((size) => (
                <button
                  key={size}
                  className={`size-card ${selectedSizes.includes(size) ? 'selected' : ''}`}
                  onClick={() => toggleSize(size)}
                >
                  <div className="size-icon">
                    <ImageIcon size={size > 64 ? 32 : size > 32 ? 24 : 16} />
                  </div>
                  <div className="size-label">{size}×{size}</div>
                  <div className="size-usage">
                    {size <= 16 ? '小图标' : 
                     size <= 32 ? '标准图标' : 
                     size <= 48 ? '大图标' : 
                     size <= 64 ? '超大图标' : 
                     size <= 128 ? '高清图标' : '超高清图标'}
                  </div>
                </button>
              ))}
            </div>
            
            <div className="conversion-actions">
              <button
                className="btn btn-primary btn-large"
                onClick={startConversion}
                disabled={isConverting || selectedSizes.length === 0}
              >
                {isConverting ? '转换中...' : `转换为ICO格式 (${selectedSizes.length}个尺寸)`}
              </button>
            </div>
          </div>
        )}

        {/* 转换进度 */}
        {isConverting && (
          <div className="progress-section">
            <div className="progress-text">正在生成ICO文件...</div>
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{ width: `${conversionProgress}%` }}
              />
            </div>
            <div className="progress-percentage">{conversionProgress}%</div>
          </div>
        )}

        {/* 步骤3: 下载结果 */}
        {results.length > 0 && (
          <div className="step-section">
            <h2 className="step-title">
              <span className="step-number">3</span>
              下载ICO文件
            </h2>
            <div className="results-grid">
              {results.map((result) => (
                <div key={result.id} className="result-card">
                  <div className="result-preview-container">
                    <img 
                      src={result.preview} 
                      alt="ICO预览" 
                      className="result-preview"
                    />
                  </div>
                  <div className="result-info">
                    <div className="result-size">{result.size}</div>
                    <div className="result-name">{currentFile?.name.replace(/\.[^/.]+$/, '.ico')}</div>
                  </div>
                  <button
                    className="btn btn-download"
                    onClick={() => {
                      const link = document.createElement('a')
                      link.href = result.downloadUrl
                      link.download = `${currentFile?.name.replace(/\.[^/.]+$/, '')}_${result.size}.ico`
                      link.click()
                    }}
                  >
                    <Download size={16} />
                    下载
                  </button>
                </div>
              ))}
            </div>
            
            <div className="results-actions">
              <button
                className="btn btn-secondary"
                onClick={clearAll}
              >
                重新开始
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default App
