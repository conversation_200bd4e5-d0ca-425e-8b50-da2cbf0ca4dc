import { useState, useRef, useCallback } from 'react'
import {
  Upload,
  Image as ImageIcon,
  Download,
  Settings,
  X,
  FileImage,
  CheckCircle,
  AlertCircle,
  Sparkles
} from 'lucide-react'
import { ImageToICOConverter } from './utils/imageConverter'
import './App.css'

interface FileItem {
  id: string
  file: File
  name: string
  size: string
  type: string
  preview?: string
}

interface ConversionResult {
  id: string
  originalName: string
  size: string
  downloadUrl: string
  preview: string
}

const ICO_SIZES = [16, 32, 48, 64, 128, 256]

function App() {
  const [files, setFiles] = useState<FileItem[]>([])
  const [selectedSizes, setSelectedSizes] = useState<number[]>([16, 32, 48])
  const [isConverting, setIsConverting] = useState(false)
  const [conversionProgress, setConversionProgress] = useState(0)
  const [results, setResults] = useState<ConversionResult[]>([])
  const [isDragOver, setIsDragOver] = useState(false)

  const fileInputRef = useRef<HTMLInputElement>(null)

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 处理文件选择
  const handleFileSelect = useCallback((selectedFiles: FileList | null) => {
    if (!selectedFiles) return

    const newFiles: FileItem[] = []

    Array.from(selectedFiles).forEach((file) => {
      // 检查文件类型
      if (file.type.startsWith('image/')) {
        const fileItem: FileItem = {
          id: Math.random().toString(36).substr(2, 9),
          file,
          name: file.name,
          size: formatFileSize(file.size),
          type: file.type
        }

        // 创建预览
        const reader = new FileReader()
        reader.onload = (e) => {
          fileItem.preview = e.target?.result as string
          setFiles(prev => prev.map(f => f.id === fileItem.id ? fileItem : f))
        }
        reader.readAsDataURL(file)

        newFiles.push(fileItem)
      }
    })

    setFiles(prev => [...prev, ...newFiles])
  }, [])

  // 拖拽处理
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    handleFileSelect(e.dataTransfer.files)
  }, [handleFileSelect])

  // 点击选择文件
  const handleFileInputClick = () => {
    fileInputRef.current?.click()
  }

  // 移除文件
  const removeFile = (id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id))
  }

  // 切换尺寸选择
  const toggleSize = (size: number) => {
    setSelectedSizes(prev =>
      prev.includes(size)
        ? prev.filter(s => s !== size)
        : [...prev, size].sort((a, b) => a - b)
    )
  }

  // 开始转换
  const startConversion = async () => {
    if (files.length === 0 || selectedSizes.length === 0) return

    setIsConverting(true)
    setConversionProgress(0)
    const newResults: ConversionResult[] = []

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i]

        // 检查文件格式
        if (!ImageToICOConverter.isSupportedImageFormat(file.file)) {
          console.warn(`不支持的文件格式: ${file.file.type}`)
          continue
        }

        // 更新进度
        setConversionProgress(((i + 0.5) / files.length) * 100)

        try {
          // 转换为ICO
          const icoBlob = await ImageToICOConverter.convertToICO(file.file, selectedSizes)

          // 创建下载URL
          const downloadUrl = URL.createObjectURL(icoBlob)

          // 创建预览URL
          const previewUrl = await ImageToICOConverter.createPreviewURL(file.file)

          const result: ConversionResult = {
            id: file.id,
            originalName: file.name,
            size: `${selectedSizes.join('×')}, ${formatFileSize(icoBlob.size)}`,
            downloadUrl,
            preview: previewUrl
          }

          newResults.push(result)
        } catch (error) {
          console.error(`转换文件 ${file.name} 时出错:`, error)
        }

        // 更新进度
        setConversionProgress(((i + 1) / files.length) * 100)
      }

      setResults(prev => [...prev, ...newResults])
    } catch (error) {
      console.error('转换过程中出错:', error)
    } finally {
      setIsConverting(false)
      setConversionProgress(0)
    }
  }

  // 清空所有数据
  const clearAll = () => {
    setFiles([])
    setResults([])
    setConversionProgress(0)
  }

  return (
    <div className="app">
      {/* 标题栏 */}
      <div className="title-bar">
        <div className="title-bar-left">
          <Sparkles className="app-icon" size={24} />
          <h1 className="app-title">ICO格式转换器</h1>
        </div>
        <div className="window-controls">
          <button className="window-control minimize" />
          <button className="window-control maximize" />
          <button className="window-control close" />
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="main-content">
        {/* 文件拖拽区域 */}
        <div
          className={`drop-zone ${isDragOver ? 'drag-over' : ''}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleFileInputClick}
        >
          <Upload className="drop-zone-icon" />
          <div className="drop-zone-text">拖拽图片文件到这里</div>
          <div className="drop-zone-subtext">
            或点击选择文件 (支持 PNG, JPG, JPEG, SVG 格式)
          </div>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*"
            style={{ display: 'none' }}
            onChange={(e) => handleFileSelect(e.target.files)}
          />
        </div>

        {/* 文件列表 */}
        {files.length > 0 && (
          <div className="file-list">
            <h3 style={{ marginBottom: '16px', color: '#1f2937' }}>选择的文件</h3>
            {files.map((file) => (
              <div key={file.id} className="file-item">
                <div className="file-info">
                  {file.preview ? (
                    <img
                      src={file.preview}
                      alt={file.name}
                      className="file-icon"
                      style={{ width: '32px', height: '32px', objectFit: 'cover', borderRadius: '4px' }}
                    />
                  ) : (
                    <FileImage className="file-icon" />
                  )}
                  <div className="file-details">
                    <h4>{file.name}</h4>
                    <p>{file.size} • {file.type}</p>
                  </div>
                </div>
                <button
                  onClick={() => removeFile(file.id)}
                  className="btn btn-secondary"
                  style={{ padding: '6px' }}
                >
                  <X size={16} />
                </button>
              </div>
            ))}
          </div>
        )}

        {/* 转换选项 */}
        {files.length > 0 && (
          <div className="conversion-options">
            <h3 className="options-title">ICO尺寸选择</h3>
            <div className="size-options">
              {ICO_SIZES.map((size) => (
                <button
                  key={size}
                  className={`size-option ${selectedSizes.includes(size) ? 'selected' : ''}`}
                  onClick={() => toggleSize(size)}
                >
                  {size}×{size}
                </button>
              ))}
            </div>

            <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
              <button
                className="btn btn-primary"
                disabled={isConverting || selectedSizes.length === 0}
                onClick={startConversion}
              >
                <Settings size={16} />
                {isConverting ? '转换中...' : '开始转换'}
              </button>

              <button
                className="btn btn-secondary"
                onClick={clearAll}
                disabled={isConverting}
              >
                清空所有
              </button>
            </div>

            {/* 进度条 */}
            {isConverting && (
              <div className="progress-bar">
                <div
                  className="progress-fill"
                  style={{ width: `${conversionProgress}%` }}
                />
              </div>
            )}
          </div>
        )}

        {/* 转换结果 */}
        {results.length > 0 && (
          <div className="results-section">
            <h3 style={{ marginBottom: '16px', color: '#1f2937' }}>转换结果</h3>
            {results.map((result) => (
              <div key={result.id} className="result-item">
                <div className="result-info">
                  <img
                    src={result.preview}
                    alt={result.originalName}
                    className="result-preview"
                  />
                  <div>
                    <h4>{result.originalName}</h4>
                    <p>{result.size}</p>
                  </div>
                  <CheckCircle size={20} color="#10b981" />
                </div>
                <button
                  className="btn btn-primary"
                  onClick={() => {
                    const link = document.createElement('a')
                    link.href = result.downloadUrl
                    link.download = result.originalName.replace(/\.[^/.]+$/, '.ico')
                    link.click()
                  }}
                >
                  <Download size={16} />
                  下载
                </button>
              </div>
            ))}
          </div>
        )}

        {/* 空状态提示 */}
        {files.length === 0 && results.length === 0 && (
          <div style={{
            textAlign: 'center',
            padding: '40px',
            color: '#6b7280',
            background: 'rgba(255, 255, 255, 0.5)',
            borderRadius: '12px',
            backdropFilter: 'blur(10px)'
          }}>
            <ImageIcon size={48} style={{ margin: '0 auto 16px', opacity: 0.5 }} />
            <h3 style={{ marginBottom: '8px', color: '#374151' }}>欢迎使用ICO格式转换器</h3>
            <p>拖拽或选择图片文件开始转换</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default App
