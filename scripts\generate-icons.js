const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// 确保输出目录存在
const outputDir = path.join(__dirname, '../public/icons');
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// 需要生成的图标尺寸
const sizes = [16, 32, 48, 64, 128, 256, 512];

async function generateIcons() {
  const svgPath = path.join(__dirname, '../public/app-icon.svg');
  
  try {
    for (const size of sizes) {
      await sharp(svgPath)
        .resize(size, size)
        .png()
        .toFile(path.join(outputDir, `icon-${size}x${size}.png`));
      
      console.log(`Generated icon-${size}x${size}.png`);
    }
    
    // 生成主图标文件
    await sharp(svgPath)
      .resize(256, 256)
      .png()
      .toFile(path.join(__dirname, '../public/app-icon.png'));
    
    console.log('Generated app-icon.png');
    console.log('All icons generated successfully!');
  } catch (error) {
    console.error('Error generating icons:', error);
  }
}

generateIcons();
