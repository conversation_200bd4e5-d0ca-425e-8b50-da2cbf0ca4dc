<svg width="256" height="256" viewBox="0 0 256 256" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 主背景渐变 -->
    <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
    </linearGradient>

    <!-- 图标渐变 -->
    <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.95" />
      <stop offset="100%" style="stop-color:#f8fafc;stop-opacity:0.9" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.25"/>
    </filter>

    <!-- 内阴影滤镜 -->
    <filter id="innerShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="2"/>
      <feGaussianBlur stdDeviation="3" result="offset-blur"/>
      <feFlood flood-color="#000000" flood-opacity="0.1"/>
      <feComposite in2="offset-blur" operator="in"/>
    </filter>
  </defs>

  <!-- 主背景圆形 -->
  <circle cx="128" cy="128" r="120" fill="url(#mainGradient)" filter="url(#shadow)"/>

  <!-- 内圆装饰 -->
  <circle cx="128" cy="128" r="110" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
  <circle cx="128" cy="128" r="100" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>

  <!-- 中央图标容器 -->
  <circle cx="128" cy="128" r="85" fill="url(#iconGradient)" filter="url(#innerShadow)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>

  <!-- 图片转换图标 -->
  <!-- 左侧图片 -->
  <rect x="70" y="95" width="35" height="28" rx="4" fill="#4f46e5" stroke="#3730a3" stroke-width="1.5"/>
  <circle cx="80" cy="105" r="3" fill="#a5b4fc"/>
  <polygon points="75,115 85,110 95,115 95,120 75,120" fill="#818cf8"/>

  <!-- 转换箭头 -->
  <path d="M115 109 L140 109" stroke="#ffffff" stroke-width="3" stroke-linecap="round"/>
  <path d="M135 104 L140 109 L135 114" stroke="#ffffff" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>

  <!-- 右侧ICO文件 -->
  <rect x="150" y="95" width="35" height="28" rx="4" fill="#10b981" stroke="#059669" stroke-width="1.5"/>

  <!-- ICO标识 -->
  <text x="167.5" y="107" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="8" font-weight="bold">ICO</text>

  <!-- 多尺寸指示器 -->
  <rect x="155" y="115" width="4" height="4" fill="#fbbf24" rx="0.5"/>
  <rect x="161" y="115" width="4" height="4" fill="#f59e0b" rx="0.5"/>
  <rect x="167" y="115" width="4" height="4" fill="#d97706" rx="0.5"/>
  <rect x="173" y="115" width="4" height="4" fill="#b45309" rx="0.5"/>

  <!-- 底部装饰性元素 -->
  <circle cx="128" cy="170" r="12" fill="rgba(255,255,255,0.2)" stroke="rgba(255,255,255,0.4)" stroke-width="1"/>
  <path d="M123 170 L128 165 L133 170" stroke="#ffffff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M128 165 L128 175" stroke="#ffffff" stroke-width="2" stroke-linecap="round"/>

  <!-- 顶部装饰点 -->
  <circle cx="128" cy="50" r="3" fill="rgba(255,255,255,0.6)"/>
  <circle cx="115" cy="60" r="2" fill="rgba(255,255,255,0.4)"/>
  <circle cx="141" cy="60" r="2" fill="rgba(255,255,255,0.4)"/>
</svg>
