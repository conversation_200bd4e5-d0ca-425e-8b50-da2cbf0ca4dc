// ICO文件格式转换工具类

interface ICOHeader {
  reserved: number;
  type: number;
  count: number;
}

interface ICODirectoryEntry {
  width: number;
  height: number;
  colorCount: number;
  reserved: number;
  planes: number;
  bitCount: number;
  bytesInRes: number;
  imageOffset: number;
}

export class ImageToICOConverter {
  
  /**
   * 将图片文件转换为ICO格式
   * @param file 原始图片文件
   * @param sizes 需要生成的ICO尺寸数组
   * @returns Promise<Blob> ICO文件的Blob对象
   */
  static async convertToICO(file: File, sizes: number[]): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = async () => {
        try {
          const icoData = await this.createICOFromImage(img, sizes);
          resolve(new Blob([icoData], { type: 'image/x-icon' }));
        } catch (error) {
          reject(error);
        }
      };
      
      img.onerror = () => {
        reject(new Error('无法加载图片'));
      };
      
      // 创建图片URL
      const reader = new FileReader();
      reader.onload = (e) => {
        img.src = e.target?.result as string;
      };
      reader.readAsDataURL(file);
    });
  }

  /**
   * 从Image对象创建ICO数据
   */
  private static async createICOFromImage(img: HTMLImageElement, sizes: number[]): Promise<ArrayBuffer> {
    const sortedSizes = [...sizes].sort((a, b) => a - b);
    const imageDataList: { size: number; data: ArrayBuffer }[] = [];
    
    // 为每个尺寸生成PNG数据
    for (const size of sortedSizes) {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        throw new Error('无法创建Canvas上下文');
      }
      
      canvas.width = size;
      canvas.height = size;
      
      // 绘制缩放后的图片
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';
      ctx.drawImage(img, 0, 0, size, size);
      
      // 转换为PNG数据
      const pngData = await this.canvasToPNG(canvas);
      imageDataList.push({ size, data: pngData });
    }
    
    // 创建ICO文件
    return this.createICOFile(imageDataList);
  }

  /**
   * 将Canvas转换为PNG数据
   */
  private static canvasToPNG(canvas: HTMLCanvasElement): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
      canvas.toBlob((blob) => {
        if (!blob) {
          reject(new Error('无法生成PNG数据'));
          return;
        }
        
        const reader = new FileReader();
        reader.onload = () => {
          resolve(reader.result as ArrayBuffer);
        };
        reader.onerror = () => {
          reject(new Error('读取PNG数据失败'));
        };
        reader.readAsArrayBuffer(blob);
      }, 'image/png');
    });
  }

  /**
   * 创建ICO文件数据
   */
  private static createICOFile(imageDataList: { size: number; data: ArrayBuffer }[]): ArrayBuffer {
    const count = imageDataList.length;
    
    // 计算文件大小
    const headerSize = 6; // ICO header
    const directorySize = count * 16; // Directory entries
    const totalImageSize = imageDataList.reduce((sum, item) => sum + item.data.byteLength, 0);
    const totalSize = headerSize + directorySize + totalImageSize;
    
    const buffer = new ArrayBuffer(totalSize);
    const view = new DataView(buffer);
    const uint8View = new Uint8Array(buffer);
    
    let offset = 0;
    
    // 写入ICO头部
    view.setUint16(offset, 0, true); // reserved
    offset += 2;
    view.setUint16(offset, 1, true); // type (1 = ICO)
    offset += 2;
    view.setUint16(offset, count, true); // count
    offset += 2;
    
    // 写入目录条目
    let imageOffset = headerSize + directorySize;
    
    for (const imageData of imageDataList) {
      const size = imageData.size;
      const width = size === 256 ? 0 : size; // ICO格式中256用0表示
      const height = size === 256 ? 0 : size;
      
      view.setUint8(offset, width); // width
      offset += 1;
      view.setUint8(offset, height); // height
      offset += 1;
      view.setUint8(offset, 0); // color count (0 for PNG)
      offset += 1;
      view.setUint8(offset, 0); // reserved
      offset += 1;
      view.setUint16(offset, 1, true); // color planes
      offset += 2;
      view.setUint16(offset, 32, true); // bits per pixel
      offset += 2;
      view.setUint32(offset, imageData.data.byteLength, true); // image size
      offset += 4;
      view.setUint32(offset, imageOffset, true); // image offset
      offset += 4;
      
      imageOffset += imageData.data.byteLength;
    }
    
    // 写入图片数据
    for (const imageData of imageDataList) {
      uint8View.set(new Uint8Array(imageData.data), offset);
      offset += imageData.data.byteLength;
    }
    
    return buffer;
  }

  /**
   * 创建预览图片URL
   */
  static createPreviewURL(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        resolve(e.target?.result as string);
      };
      reader.onerror = () => {
        reject(new Error('无法创建预览'));
      };
      reader.readAsDataURL(file);
    });
  }

  /**
   * 验证文件是否为支持的图片格式
   */
  static isSupportedImageFormat(file: File): boolean {
    const supportedTypes = [
      'image/png',
      'image/jpeg',
      'image/jpg',
      'image/svg+xml',
      'image/gif',
      'image/bmp',
      'image/webp'
    ];
    
    return supportedTypes.includes(file.type);
  }

  /**
   * 获取图片尺寸信息
   */
  static getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        resolve({ width: img.naturalWidth, height: img.naturalHeight });
      };
      
      img.onerror = () => {
        reject(new Error('无法获取图片尺寸'));
      };
      
      const reader = new FileReader();
      reader.onload = (e) => {
        img.src = e.target?.result as string;
      };
      reader.readAsDataURL(file);
    });
  }
}

export default ImageToICOConverter;
