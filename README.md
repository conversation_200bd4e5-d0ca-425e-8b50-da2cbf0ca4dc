# ICO格式转换器

一个简单易用的桌面应用程序，可以将各种格式的图片（PNG、JPG、JPEG、SVG、GIF、BMP、WebP）转换为ICO格式。

## 功能特性

- 🖼️ **多格式支持**: 支持 PNG、JPG、JPEG、SVG、GIF、BMP、WebP 等常见图片格式
- 📏 **多尺寸输出**: 支持生成 16×16、32×32、48×48、64×64、128×128、256×256 像素的ICO文件
- 🎯 **拖拽上传**: 支持直接拖拽图片文件到应用窗口
- 📁 **批量处理**: 可以同时处理多个图片文件
- 💾 **一键下载**: 转换完成后可直接下载ICO文件
- 🎨 **桌面应用风格**: 原生桌面应用界面，非网页风格
- ⚡ **高质量转换**: 使用高质量图片缩放算法，确保转换质量

## 使用方法

### 1. 添加图片文件
- **拖拽方式**: 直接将图片文件拖拽到应用窗口的拖拽区域
- **点击选择**: 点击拖拽区域，在弹出的文件选择器中选择图片文件
- **菜单选择**: 使用菜单栏的"文件" → "打开图片..."选项

### 2. 选择ICO尺寸
- 在"ICO尺寸选择"区域中，点击需要的尺寸按钮
- 可以选择多个尺寸，生成的ICO文件将包含所有选中的尺寸
- 默认选择了 16×16、32×32、48×48 三种常用尺寸

### 3. 开始转换
- 点击"开始转换"按钮
- 应用会显示转换进度
- 转换完成后，结果会显示在"转换结果"区域

### 4. 下载ICO文件
- 在转换结果中，点击"下载"按钮
- ICO文件会自动下载到默认下载目录

## 开发和构建

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建应用
```bash
npm run build
```

构建完成后，可执行文件会生成在 `release` 目录中。

## 技术栈

- **前端框架**: React + TypeScript
- **桌面框架**: Electron
- **构建工具**: Vite
- **图片处理**: Canvas API + Sharp
- **UI组件**: Lucide React (图标)
- **样式**: CSS3 (渐变背景、毛玻璃效果)

## 支持的图片格式

| 格式 | 扩展名 | 支持状态 |
|------|--------|----------|
| PNG | .png | ✅ |
| JPEG | .jpg, .jpeg | ✅ |
| SVG | .svg | ✅ |
| GIF | .gif | ✅ |
| BMP | .bmp | ✅ |
| WebP | .webp | ✅ |

## ICO尺寸说明

| 尺寸 | 用途 |
|------|------|
| 16×16 | 小图标、任务栏 |
| 32×32 | 桌面图标、文件夹 |
| 48×48 | 大图标显示 |
| 64×64 | 高分辨率显示 |
| 128×128 | 超大图标 |
| 256×256 | 高清显示 |

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
