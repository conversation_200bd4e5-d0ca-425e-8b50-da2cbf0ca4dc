{"name": "ico-converter", "productName": "ICO格式转换器", "description": "一个简单易用的图片转ICO格式工具", "version": "1.0.0", "author": "ICO Converter Team", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build && electron-builder", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@types/file-saver": "^2.0.7", "canvas": "^3.1.2", "lucide-react": "^0.534.0", "react": "^18.2.0", "react-dom": "^18.2.0", "sharp": "^0.34.3"}, "devDependencies": {"@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "electron": "^30.0.1", "electron-builder": "^24.13.3", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.1.6", "vite-plugin-electron": "^0.28.6", "vite-plugin-electron-renderer": "^0.14.5"}, "main": "dist-electron/main.js"}